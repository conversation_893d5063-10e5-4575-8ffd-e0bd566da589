# Volatility Indicators Manual

## Overview

The dispersion charts feature three distinct volatility detection systems that help identify different types of market volatility events. Each indicator serves a specific purpose and appears on different charts within the dashboard.

## 1. Volatility Dots 🔵

### **Purpose**
Detect periods when multiple currency pairs of the same currency are experiencing significant volatility simultaneously.

### **Location**
- **Chart**: Rolling Dispersion of CSSD by Currency (first chart)
- **Position**: Displayed in 1/8th sections by currency (USD=top, EUR=2nd, etc.)

### **Detection Criteria**
- At least 3 pairs of the same currency have absolute log returns above **2.5x standard deviation**
- Condition must persist for **3+ consecutive periods**
- Uses global standard deviation threshold for consistency

### **Visual Appearance**
- Small colored circles (currency-colored)
- Size: 3 pixels with white border
- Opacity: 60% to appear in background
- Positioned in currency-specific horizontal bands

### **How to Use**
1. **Identify Currency-Specific Volatility**: Look for dots in specific currency sections to see which currency is experiencing volatility
2. **Timing Analysis**: Dots show exact timing of volatility events
3. **Intensity Assessment**: More dots = more frequent volatility events
4. **Cross-Currency Comparison**: Compare dot frequency across different currency sections

---

## 2. Vertical Volatility Lines 📏

### **Purpose**
Mark significant rate-of-change (ROC) events that indicate sudden price movements.

### **Location**
- **Chart**: Rolling Dispersion of CSSD by Currency (first chart)
- **Position**: Vertical lines spanning currency sections when ROC exceeds threshold

### **Detection Criteria**
- 5-bar rate-of-change (ROC) exceeds **1 standard deviation** of means
- Based on percentage change over 5-period window
- Triggers on sudden directional movements

### **Visual Appearance**
- Thin vertical lines (width: 1 pixel)
- Currency-colored based on which currency triggered the event
- Segmented by currency position when multiple currencies trigger simultaneously
- Full height or sectioned depending on number of simultaneous triggers

### **How to Use**
1. **Breakout Detection**: Vertical lines often mark the start of significant price movements
2. **Momentum Confirmation**: Use with other indicators to confirm trend changes
3. **Entry/Exit Timing**: Lines can signal optimal entry or exit points
4. **Market Event Identification**: Cluster of lines may indicate major market events

---

## 3. Volatility Signal Stars ⭐

### **Purpose**
Identify optimal volatility trading opportunities based on sophisticated multi-criteria analysis.

### **Location**
- **Chart**: Rolling Dispersion of Normalized Returns CSSD (second chart)
- **Position**: Currency-specific sections (same positioning as dots)

### **Detection Criteria (3-Step Process)**
1. **Pre-Quiet Condition**: At least 50% of previous 10 minutes below 30th percentile (market was relatively calm)
2. **Persistence Condition**: At least 60% of last 2 bars above 75th percentile (sustained volatility spike)
3. **Unison Condition**: At least 1 pair with >0.2 correlation (coordinated movement)

### **Visual Appearance**
- Star symbols (★)
- Size: 12 pixels with white border
- Currency-colored
- Positioned in currency-specific horizontal bands

### **How to Use**
1. **High-Probability Setups**: Stars indicate the highest-quality volatility trading opportunities
2. **Entry Signals**: Consider entering volatility trades when stars appear
3. **Risk Management**: Stars suggest periods when volatility strategies are most likely to succeed
4. **Portfolio Timing**: Use stars to time portfolio adjustments or hedging strategies

---

## Practical Trading Applications

### **Scalping Strategy**
- **Dots**: Quick entries on currency-specific volatility
- **Vertical Lines**: Immediate breakout trades
- **Stars**: High-confidence volatility plays

### **Swing Trading**
- **Dots**: Identify currencies entering volatile phases
- **Vertical Lines**: Confirm trend direction changes
- **Stars**: Time major position adjustments

### **Risk Management**
- **Dots**: Monitor exposure to volatile currencies
- **Vertical Lines**: Prepare for sudden market moves
- **Stars**: Optimal times to implement hedging strategies

### **Portfolio Optimization**
- **Dots**: Rebalance when specific currencies become volatile
- **Vertical Lines**: Adjust position sizes on momentum shifts
- **Stars**: Execute major portfolio restructuring

---

## Best Practices

### **Combining Indicators**
1. **Confirmation**: Use multiple indicators together for higher confidence
2. **Timing**: Stars for strategy, lines for execution, dots for monitoring
3. **Currency Focus**: Track specific currencies across all three indicators

### **Market Context**
1. **Session Timing**: Consider which trading sessions are active
2. **Economic Events**: Correlate indicators with scheduled news releases
3. **Market Conditions**: Adjust sensitivity based on overall market volatility

### **Risk Considerations**
1. **False Signals**: No indicator is 100% accurate - use proper risk management
2. **Market Gaps**: Indicators may not capture overnight or weekend gaps
3. **Correlation Changes**: Currency relationships can shift during major events

---

## Technical Notes

### **Data Requirements**
- Minimum 240 data points for reliable threshold calculations
- Real-time updates every minute
- Normalized log returns for accurate cross-currency comparison

### **Customization Options**
- Threshold multipliers can be adjusted for sensitivity
- Time windows can be modified for different trading styles
- Color schemes can be customized for better visibility

### **Performance Considerations**
- Indicators update in real-time with new data
- Historical recalculation occurs when parameters change
- Computational load increases with more currency pairs
